<?php
session_start();
require '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $verification_id = $_POST['verification_id'];
    $action = $_POST['action'];
    $verified_by = $_SESSION['id_petugas'];
    $verified_at = date('Y-m-d H:i:s');

    if ($action == 'verify') {
        $catatan = $_POST['catatan'] ?? '';

        // Update verifikasi - mark as verified
        $update_verification = mysqli_query($koneksi,
            "UPDATE verifikasi SET
             catatan='$catatan',
             verified_by='$verified_by',
             verified_at='$verified_at'
             WHERE id='$verification_id'"
        );

        if ($update_verification) {
            // Ambil tugas_id dari verifikasi
            $get_task = mysqli_query($koneksi, "SELECT tugas_id FROM verifikasi WHERE id='$verification_id'");
            $task_data = mysqli_fetch_array($get_task);
            $tugas_id = $task_data['tugas_id'];

            // Update tugas proyek menjadi selesai dan progress 100%
            $update_task = mysqli_query($koneksi,
                "UPDATE tugas_proyek SET
                 status='selesai',
                 progress_percentage=100,
                 tgl_selesai=CURDATE(),
                 verified_by='$verified_by',
                 verified_at='$verified_at'
                 WHERE id='$tugas_id'"
            );

            $_SESSION['success_message'] = 'Verifikasi berhasil diproses!';
        } else {
            $_SESSION['error_message'] = 'Gagal memproses verifikasi: ' . mysqli_error($koneksi);
        }

    } elseif ($action == 'reset') {
        // Reset verifikasi
        $update_verification = mysqli_query($koneksi,
            "UPDATE verifikasi SET
             catatan=NULL,
             verified_by=NULL,
             verified_at=NULL
             WHERE id='$verification_id'"
        );

        if ($update_verification) {
            // Ambil tugas_id dari verifikasi
            $get_task = mysqli_query($koneksi, "SELECT tugas_id FROM verifikasi WHERE id='$verification_id'");
            $task_data = mysqli_fetch_array($get_task);
            $tugas_id = $task_data['tugas_id'];

            // Reset tugas proyek ke status proses
            $update_task = mysqli_query($koneksi,
                "UPDATE tugas_proyek SET
                 status='proses',
                 progress_percentage=75,
                 tgl_selesai=NULL,
                 verified_by=NULL,
                 verified_at=NULL
                 WHERE id='$tugas_id'"
            );

            $_SESSION['success_message'] = 'Verifikasi berhasil direset!';
        } else {
            $_SESSION['error_message'] = 'Gagal mereset verifikasi: ' . mysqli_error($koneksi);
        }
    }

    header("Location: verifikasi.php");
    exit;
} else {
    header("Location: verifikasi.php");
    exit;
}
?>
