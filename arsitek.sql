-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- <PERSON><PERSON><PERSON> pembuatan: 23 Jun 2025 pada 07.44
-- Versi server: 10.4.32-MariaDB
-- Versi PHP: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `arsitek`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON> dari tabel `file_gambar`
--

CREATE TABLE `file_gambar` (
  `id` int(11) NOT NULL,
  `deskripsi` varchar(200) NOT NULL,
  `gambar` varchar(200) NOT NULL,
  `tugas_id` int(11) DEFAULT NULL,
  `uploaded_by` int(11) DEFAULT NULL,
  `file_size` bigint(20) DEFAULT NULL,
  `file_type` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `file_gambar`
--

INSERT INTO `file_gambar` (`id`, `deskripsi`, `gambar`, `tugas_id`, `uploaded_by`, `file_size`, `file_type`) VALUES
(1, 'Desain Konsep Awal - PDF', '1750656699_TUGAS_8_-_KELOMPOK_4.pdf', 1, 1, 2048576, 'pdf'),
(2, 'Dokumentasi Teknis', '1750656816_24516-86669-1-PB.pdf', 3, 1, 1536000, 'pdf'),
(3, 'File CAD Desain', '1750656917_sample1.dwg', 3, 1, 5242880, 'dwg'),
(4, 'Gambar Referensi Interior', '1750657264_image_1.jpg', 5, 1, 1024000, 'jpg');

-- --------------------------------------------------------

--
-- Struktur dari tabel `login_logs`
--

CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `status` enum('success','fail') DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `login_logs`
--

INSERT INTO `login_logs` (`id`, `user_id`, `email`, `ip_address`, `status`, `created_at`) VALUES
(1, 1, '<EMAIL>', '::1', 'success', '2025-06-07 12:58:43');

-- --------------------------------------------------------

--
-- Struktur dari tabel `password_resets`
--

CREATE TABLE `password_resets` (
  `id` int(11) NOT NULL,
  `email` varchar(100) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Struktur dari tabel `petugas`
--

CREATE TABLE `petugas` (
  `id_petugas` int(11) NOT NULL,
  `nama_petugas` varchar(200) NOT NULL,
  `username` varchar(200) NOT NULL,
  `password` varchar(200) NOT NULL,
  `level` enum('proyek','admin','client') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `petugas`
--

INSERT INTO `petugas` (`id_petugas`, `nama_petugas`, `username`, `password`, `level`) VALUES
(1, 'lingga', 'lingga', '11111', 'proyek'),
(2, 'ian', 'ian', '11111', 'admin'),
(3, 'vicky', 'vicky', '11111', 'client');

-- --------------------------------------------------------

--
-- Struktur dari tabel `tugas_proyek`
--

CREATE TABLE `tugas_proyek` (
  `id` int(11) NOT NULL,
  `nama_kegiatan` varchar(200) NOT NULL,
  `deskripsi` varchar(200) NOT NULL,
  `tgl` date NOT NULL,
  `tgl_mulai` date DEFAULT NULL,
  `tgl_selesai` date DEFAULT NULL,
  `status` enum('pending','proses','selesai','batal','verifikasi') NOT NULL DEFAULT 'pending',
  `progress_percentage` int(3) DEFAULT 0,
  `verified_by` int(11) DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `tugas_proyek`
--

INSERT INTO `tugas_proyek` (`id`, `nama_kegiatan`, `deskripsi`, `tgl`, `tgl_mulai`, `tgl_selesai`, `status`, `progress_percentage`, `verified_by`, `verified_at`) VALUES
(1, 'Desain Konsep Awal', 'Membuat konsep desain arsitektur dasar', '2025-06-23', '2025-06-20', '2025-06-23', 'selesai', 100, 1, '2025-06-23 10:30:00'),
(2, 'Survey Lokasi', 'Melakukan survey dan pengukuran lokasi proyek', '2025-06-23', '2025-06-21', NULL, 'proses', 75, NULL, NULL),
(3, 'Gambar Teknik', 'Pembuatan gambar teknik detail', '2025-06-24', '2025-06-22', '2025-06-24', 'selesai', 100, 1, '2025-06-24 14:15:00'),
(4, 'Perhitungan Struktur', 'Analisis dan perhitungan struktur bangunan', '2025-06-15', '2025-06-12', NULL, 'proses', 60, NULL, NULL),
(5, 'Desain Interior', 'Perencanaan tata ruang interior', '2025-06-16', '2025-06-14', '2025-06-16', 'selesai', 100, 1, '2025-06-16 16:45:00'),
(6, 'Estimasi Biaya', 'Perhitungan estimasi biaya konstruksi', '2025-06-25', NULL, NULL, 'pending', 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Struktur dari tabel `verifikasi`
--

CREATE TABLE `verifikasi` (
  `id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL,
  `tugas_id` int(11) NOT NULL,
  `catatan` text DEFAULT NULL,
  `verified_by` int(11) DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `verifikasi`
--

INSERT INTO `verifikasi` (`id`, `file_id`, `tugas_id`, `catatan`, `verified_by`, `verified_at`) VALUES
(1, 1, 1, 'Desain konsep sudah sesuai dengan brief client', 1, '2025-06-23 10:30:00'),
(2, 2, 3, 'Gambar teknik detail sudah akurat', 1, '2025-06-24 14:15:00'),
(3, 4, 5, 'Desain interior sesuai dengan konsep yang diinginkan', 1, '2025-06-16 16:45:00');

-- --------------------------------------------------------

--
-- Struktur dari tabel `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','client') DEFAULT 'client',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `users`
--

INSERT INTO `users` (`id`, `first_name`, `last_name`, `email`, `password`, `role`, `created_at`) VALUES
(1, 'iann', 'nub', '<EMAIL>', '$2y$10$T4JDiPA62j/UIyd7alP62uPnBaH3A6Ia7ImTbvb6smSSM6X9Yy60q', 'admin', '2025-06-07 12:58:37');

--
-- Indexes for dumped tables
--

--
-- Indeks untuk tabel `file_gambar`
--
ALTER TABLE `file_gambar`
  ADD PRIMARY KEY (`id`),
  ADD KEY `tugas_id` (`tugas_id`),
  ADD KEY `uploaded_by` (`uploaded_by`);

--
-- Indeks untuk tabel `login_logs`
--
ALTER TABLE `login_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indeks untuk tabel `password_resets`
--
ALTER TABLE `password_resets`
  ADD PRIMARY KEY (`id`);

--
-- Indeks untuk tabel `petugas`
--
ALTER TABLE `petugas`
  ADD PRIMARY KEY (`id_petugas`);

--
-- Indeks untuk tabel `tugas_proyek`
--
ALTER TABLE `tugas_proyek`
  ADD PRIMARY KEY (`id`),
  ADD KEY `verified_by` (`verified_by`);

--
-- Indeks untuk tabel `verifikasi`
--
ALTER TABLE `verifikasi`
  ADD PRIMARY KEY (`id`),
  ADD KEY `file_id` (`file_id`),
  ADD KEY `tugas_id` (`tugas_id`),
  ADD KEY `verified_by` (`verified_by`);

--
-- Indeks untuk tabel `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT untuk tabel yang dibuang
--

--
-- AUTO_INCREMENT untuk tabel `file_gambar`
--
ALTER TABLE `file_gambar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT untuk tabel `login_logs`
--
ALTER TABLE `login_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT untuk tabel `password_resets`
--
ALTER TABLE `password_resets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT untuk tabel `petugas`
--
ALTER TABLE `petugas`
  MODIFY `id_petugas` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT untuk tabel `tugas_proyek`
--
ALTER TABLE `tugas_proyek`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT untuk tabel `verifikasi`
--
ALTER TABLE `verifikasi`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT untuk tabel `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints untuk tabel yang dibuang
--

--
-- Constraints untuk tabel `file_gambar`
--
ALTER TABLE `file_gambar`
  ADD CONSTRAINT `file_gambar_ibfk_1` FOREIGN KEY (`tugas_id`) REFERENCES `tugas_proyek` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `file_gambar_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `petugas` (`id_petugas`) ON DELETE SET NULL;

--
-- Constraints untuk tabel `tugas_proyek`
--
ALTER TABLE `tugas_proyek`
  ADD CONSTRAINT `tugas_proyek_ibfk_1` FOREIGN KEY (`verified_by`) REFERENCES `petugas` (`id_petugas`) ON DELETE SET NULL;

--
-- Constraints untuk tabel `verifikasi`
--
ALTER TABLE `verifikasi`
  ADD CONSTRAINT `verifikasi_ibfk_1` FOREIGN KEY (`file_id`) REFERENCES `file_gambar` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `verifikasi_ibfk_2` FOREIGN KEY (`verified_by`) REFERENCES `petugas` (`id_petugas`) ON DELETE SET NULL,
  ADD CONSTRAINT `verifikasi_ibfk_3` FOREIGN KEY (`tugas_id`) REFERENCES `tugas_proyek` (`id`) ON DELETE CASCADE;

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
