<?php
require 'koneksi.php';

/**
 * Update task progress based on file approval status
 * This function is called when a file status changes
 */
function updateTaskProgressFromFileStatus($koneksi, $file_id) {
    // Get file information
    $file_query = "SELECT fg.*, tp.id as task_id, tp.nama_kegiatan 
                   FROM file_gambar fg 
                   LEFT JOIN tugas_proyek tp ON fg.tugas_id = tp.id 
                   WHERE fg.id = ?";
    $file_stmt = mysqli_prepare($koneksi, $file_query);
    mysqli_stmt_bind_param($file_stmt, "i", $file_id);
    mysqli_stmt_execute($file_stmt);
    $file_result = mysqli_stmt_get_result($file_stmt);
    
    if (!$file_result || mysqli_num_rows($file_result) == 0) {
        return false;
    }
    
    $file_data = mysqli_fetch_assoc($file_result);
    
    if (!$file_data['task_id']) {
        // File not linked to any task
        return true;
    }
    
    $task_id = $file_data['task_id'];
    
    // Count files for this task
    $count_query = "SELECT 
                        COUNT(*) as total_files,
                        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_files,
                        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_files,
                        SUM(CASE WHEN status = 'revision' THEN 1 ELSE 0 END) as revision_files
                    FROM file_gambar 
                    WHERE tugas_id = ?";
    $count_stmt = mysqli_prepare($koneksi, $count_query);
    mysqli_stmt_bind_param($count_stmt, "i", $task_id);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $count_data = mysqli_fetch_assoc($count_result);
    
    $total_files = $count_data['total_files'];
    $approved_files = $count_data['approved_files'];
    $rejected_files = $count_data['rejected_files'];
    $revision_files = $count_data['revision_files'];
    
    // Calculate progress and status
    $new_status = 'proses';
    $new_progress = 0;
    
    if ($total_files > 0) {
        // Calculate progress percentage based on approved files
        $new_progress = round(($approved_files / $total_files) * 100);
        
        // Determine task status
        if ($approved_files == $total_files) {
            // All files approved
            $new_status = 'selesai';
            $new_progress = 100;
        } elseif ($rejected_files > 0 || $revision_files > 0) {
            // Some files rejected or need revision
            $new_status = 'proses';
        } else {
            // Files pending or in progress
            $new_status = 'proses';
        }
    }
    
    // Update task
    $update_query = "UPDATE tugas_proyek 
                     SET status = ?, progress_percentage = ?, updated_at = NOW() 
                     WHERE id = ?";
    $update_stmt = mysqli_prepare($koneksi, $update_query);
    mysqli_stmt_bind_param($update_stmt, "sii", $new_status, $new_progress, $task_id);
    
    if (mysqli_stmt_execute($update_stmt)) {
        // Log the update
        error_log("Task $task_id updated: status=$new_status, progress=$new_progress% (based on file approvals)");
        return true;
    }
    
    return false;
}

/**
 * Sync all task progress based on file approval status
 * This can be run periodically to ensure consistency
 */
function syncAllTaskProgress($koneksi) {
    // Get all tasks that have files
    $tasks_query = "SELECT DISTINCT tp.id, tp.nama_kegiatan 
                    FROM tugas_proyek tp 
                    INNER JOIN file_gambar fg ON tp.id = fg.tugas_id";
    $tasks_result = mysqli_query($koneksi, $tasks_query);
    
    $updated_count = 0;
    
    while ($task = mysqli_fetch_assoc($tasks_result)) {
        // Get a file ID for this task to trigger update
        $file_query = "SELECT id FROM file_gambar WHERE tugas_id = ? LIMIT 1";
        $file_stmt = mysqli_prepare($koneksi, $file_query);
        mysqli_stmt_bind_param($file_stmt, "i", $task['id']);
        mysqli_stmt_execute($file_stmt);
        $file_result = mysqli_stmt_get_result($file_stmt);
        
        if ($file_result && mysqli_num_rows($file_result) > 0) {
            $file_data = mysqli_fetch_assoc($file_result);
            if (updateTaskProgressFromFileStatus($koneksi, $file_data['id'])) {
                $updated_count++;
            }
        }
    }
    
    return $updated_count;
}

/**
 * Create verification entry when file is uploaded
 */
function createVerificationForFile($koneksi, $file_id) {
    // Check if verification already exists
    $check_query = "SELECT id FROM verifikasi WHERE file_id = ?";
    $check_stmt = mysqli_prepare($koneksi, $check_query);
    mysqli_stmt_bind_param($check_stmt, "i", $file_id);
    mysqli_stmt_execute($check_stmt);
    $check_result = mysqli_stmt_get_result($check_stmt);

    if (mysqli_num_rows($check_result) > 0) {
        return true; // Already exists
    }

    // Get tugas_id from file_gambar table
    $file_query = "SELECT tugas_id FROM file_gambar WHERE id = ?";
    $file_stmt = mysqli_prepare($koneksi, $file_query);
    mysqli_stmt_bind_param($file_stmt, "i", $file_id);
    mysqli_stmt_execute($file_stmt);
    $file_result = mysqli_stmt_get_result($file_stmt);

    if (!$file_result || mysqli_num_rows($file_result) == 0) {
        return false; // File not found
    }

    $file_data = mysqli_fetch_assoc($file_result);
    $tugas_id = $file_data['tugas_id'];

    if (!$tugas_id) {
        return false; // No tugas_id associated with this file
    }

    // Create verification entry for all uploaded files
    $insert_query = "INSERT INTO verifikasi (file_id, tugas_id, created_at)
                     VALUES (?, ?, NOW())";
    $insert_stmt = mysqli_prepare($koneksi, $insert_query);
    mysqli_stmt_bind_param($insert_stmt, "ii", $file_id, $tugas_id);

    return mysqli_stmt_execute($insert_stmt);
}

/**
 * Update verification when file is uploaded (simplified)
 * Since we removed status tracking, this function now just ensures verification entry exists
 */
function updateVerificationFromFileStatus($koneksi, $file_id) {
    // Check if verification entry exists for this file
    $check_query = "SELECT id FROM verifikasi WHERE file_id = ?";
    $check_stmt = mysqli_prepare($koneksi, $check_query);
    mysqli_stmt_bind_param($check_stmt, "i", $file_id);
    mysqli_stmt_execute($check_stmt);
    $check_result = mysqli_stmt_get_result($check_stmt);

    if (!$check_result || mysqli_num_rows($check_result) == 0) {
        // Create verification entry if it doesn't exist
        return createVerificationForFile($koneksi, $file_id);
    }

    return true; // Verification entry already exists
}

// Handle AJAX calls for manual sync
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    // Start session only if not already started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['nama']) || ($_SESSION['level'] != 'admin' && $_SESSION['level'] != 'proyek')) {
        http_response_code(403);
        die('Access denied');
    }
    
    $action = $_POST['action'];
    $response = ['success' => false, 'message' => 'Invalid action'];
    
    switch ($action) {
        case 'sync_progress':
            $updated_count = syncAllTaskProgress($koneksi);
            $response = [
                'success' => true, 
                'message' => "Berhasil sinkronisasi $updated_count tugas",
                'updated_count' => $updated_count
            ];
            break;
            
        case 'update_task_from_file':
            $file_id = intval($_POST['file_id'] ?? 0);
            if ($file_id > 0) {
                if (updateTaskProgressFromFileStatus($koneksi, $file_id)) {
                    $response = ['success' => true, 'message' => 'Progress tugas berhasil diupdate'];
                } else {
                    $response = ['success' => false, 'message' => 'Gagal mengupdate progress tugas'];
                }
            } else {
                $response = ['success' => false, 'message' => 'File ID tidak valid'];
            }
            break;
    }
    
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}
?>
