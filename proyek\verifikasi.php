<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>SB Admin 2 - Dashboard</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

</head>


<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="index.html">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">APPEMSZ</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="proyek.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>
            </li>


            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Interface
            </div>



            <li class="nav-item">
                <a class="nav-link" href="tugas_harian.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Tugas harian</span></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="uploud_file.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Uploud file desain</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="verifikasi.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>verifikasi</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="input_tugas.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>input tugas harian</span></a>
            </li>
            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">
                <div class="container-fluid mt-4">

                    <!-- Judul Halaman -->
                    <h1 class="h3 mb-4 text-gray-800">
                        <i class="fas fa-check-double mr-2"></i>Verifikasi Tugas & Desain Proyek
                    </h1>

                    <!-- Stats Cards -->
                    <div class="row mb-4">
                        <?php
                        require '../koneksi.php';

                        // Hitung statistik verifikasi
                        $total_verifications = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM verifikasi"))['count'];
                        $verified_count = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM verifikasi WHERE verified_by IS NOT NULL"))['count'];
                        $unverified_count = $total_verifications - $verified_count;
                        ?>

                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Verifikasi</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_verifications; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-list fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Sudah Diverifikasi</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $verified_count; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-4 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Belum Diverifikasi</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $unverified_count; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabel Verifikasi -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-list-check mr-2"></i>Daftar Verifikasi Tugas & File
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>No</th>
                                            <th>Nama Tugas</th>
                                            <th>Deskripsi</th>
                                            <th>File Terkait</th>
                                            <th>Progress</th>
                                            <th>Status Verifikasi</th>
                                            <th>Catatan</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Query untuk mengambil data verifikasi dengan join
                                        $query = "SELECT v.*, tp.nama_kegiatan, tp.deskripsi, tp.progress_percentage,
                                                         fg.deskripsi as file_desc, fg.gambar as file_name,
                                                         p.nama_petugas as verified_by_name
                                                  FROM verifikasi v
                                                  JOIN tugas_proyek tp ON v.tugas_id = tp.id
                                                  LEFT JOIN file_gambar fg ON v.file_id = fg.id
                                                  LEFT JOIN petugas p ON v.verified_by = p.id_petugas
                                                  ORDER BY v.created_at DESC";

                                        $result = mysqli_query($koneksi, $query);
                                        $no = 1;

                                        while($row = mysqli_fetch_array($result)) {
                                            // Tentukan status verifikasi berdasarkan apakah sudah diverifikasi atau belum
                                            $is_verified = !empty($row['verified_by']);
                                            $badge_class = $is_verified ? 'badge-success' : 'badge-warning';
                                            $status_text = $is_verified ? 'Sudah Diverifikasi' : 'Belum Diverifikasi';
                                        ?>
                                        <tr>
                                            <td><?php echo $no++; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($row['nama_kegiatan']); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($row['deskripsi']); ?></td>
                                            <td>
                                                <?php if($row['file_name']): ?>
                                                    <a href="../file_handler.php?id=<?php echo $row['file_id']; ?>&action=view" target="_blank" class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye mr-1"></i>Lihat File
                                                    </a>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($row['file_desc']); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">Tidak ada file</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-info" role="progressbar"
                                                         style="width: <?php echo $row['progress_percentage']; ?>%"
                                                         aria-valuenow="<?php echo $row['progress_percentage']; ?>"
                                                         aria-valuemin="0" aria-valuemax="100">
                                                        <?php echo $row['progress_percentage']; ?>%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo $badge_class; ?>"><?php echo $status_text; ?></span>
                                                <?php if($row['verified_by_name']): ?>
                                                    <br><small class="text-muted">oleh: <?php echo htmlspecialchars($row['verified_by_name']); ?></small>
                                                    <?php if($row['verified_at']): ?>
                                                        <br><small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($row['verified_at'])); ?></small>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($row['catatan']): ?>
                                                    <small class="text-info"><?php echo htmlspecialchars($row['catatan']); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if(!$is_verified): ?>
                                                    <button class="btn btn-success btn-sm" onclick="verifyTask(<?php echo $row['id']; ?>)">
                                                        <i class="fas fa-check mr-1"></i>Verifikasi
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-secondary btn-sm" onclick="resetVerification(<?php echo $row['id']; ?>)">
                                                        <i class="fas fa-undo mr-1"></i>Reset
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php } ?>

                                        <?php if(mysqli_num_rows($result) == 0): ?>
                                        <tr>
                                            <td colspan="8" class="text-center text-muted">
                                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                                <br>Belum ada data verifikasi
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
            </div>


            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy;FOKUS UKK!!</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->


        <!-- Scroll to Top Button-->
        <a class="scroll-to-top rounded" href="#page-top">
            <i class="fas fa-angle-up"></i>
        </a>

        <!-- Logout Modal-->
        <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                        <a class="btn btn-primary" href="login.php">Logout</a>
                    </div>
                </div>
            </div>
        </div>




    </div> <!-- End of Content Wrapper -->
    </div> <!-- End of Page Wrapper -->

    <!-- Bootstrap core JavaScript -->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript -->
    <script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages -->
    <script src="../tmp/js/sb-admin-2.min.js"></script>

    <!-- Optional: Chart plugin -->
    <script src="../tmp/vendor/chart.js/Chart.min.js"></script>
    <script src="../tmp/js/demo/chart-area-demo.js"></script>
    <script src="../tmp/js/demo/chart-pie-demo.js"></script>

    <!-- Custom JavaScript for Verification -->
    <script>
    function verifyTask(verificationId) {
        const catatan = prompt('Masukkan catatan verifikasi (opsional):');

        if(catatan !== null) { // User didn't cancel
            if(confirm('Apakah Anda yakin ingin memverifikasi tugas ini?')) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'proses_verifikasi.php';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'verification_id';
                idInput.value = verificationId;

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'verify';

                const catatanInput = document.createElement('input');
                catatanInput.type = 'hidden';
                catatanInput.name = 'catatan';
                catatanInput.value = catatan || '';

                form.appendChild(idInput);
                form.appendChild(actionInput);
                form.appendChild(catatanInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    }

    function resetVerification(verificationId) {
        if(confirm('Apakah Anda yakin ingin mereset verifikasi ini?')) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'proses_verifikasi.php';

            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'verification_id';
            idInput.value = verificationId;

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'reset';

            form.appendChild(idInput);
            form.appendChild(actionInput);
            document.body.appendChild(form);
            form.submit();
        }
    }

    // Auto refresh every 30 seconds
    setTimeout(function() {
        location.reload();
    }, 30000);
    </script>

</body>

</html>