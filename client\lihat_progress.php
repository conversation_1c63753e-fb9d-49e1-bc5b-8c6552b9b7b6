<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

// Ambil data tugas proyek untuk ditampilkan ke client
$query = "SELECT * FROM tugas_proyek ORDER BY tgl DESC";
$result = mysqli_query($koneksi, $query);

// Hitung statistik
$query_total = "SELECT COUNT(*) as total FROM tugas_proyek";
$result_total = mysqli_query($koneksi, $query_total);
$total_tugas = mysqli_fetch_array($result_total)['total'];

$query_selesai = "SELECT COUNT(*) as selesai FROM tugas_proyek WHERE status='selesai'";
$result_selesai = mysqli_query($koneksi, $query_selesai);
$tugas_selesai = mysqli_fetch_array($result_selesai)['selesai'];

$query_proses = "SELECT COUNT(*) as proses FROM tugas_proyek WHERE status='proses'";
$result_proses = mysqli_query($koneksi, $query_proses);
$tugas_proses = mysqli_fetch_array($result_proses)['proses'];

$query_batal = "SELECT COUNT(*) as batal FROM tugas_proyek WHERE status='batal'";
$result_batal = mysqli_query($koneksi, $query_batal);
$tugas_batal = mysqli_fetch_array($result_batal)['batal'];

// Hitung persentase progress
$progress_percentage = $total_tugas > 0 ? round(($tugas_selesai / $total_tugas) * 100, 1) : 0;
?>
<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Lihat Progress</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <!-- Custom CSS for Excel-like Table -->
    <style>
        .excel-container {
            background: #ffffff;
            border: 1px solid #d1d3e2;
            border-radius: 0;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            overflow: hidden;
        }

        .excel-toolbar {
            background: #f8f9fc;
            border-bottom: 1px solid #d1d3e2;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .excel-toolbar-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .excel-toolbar-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .excel-search {
            position: relative;
        }

        .excel-search input {
            border: 1px solid #d1d3e2;
            border-radius: 4px;
            padding: 6px 12px 6px 35px;
            font-size: 0.875rem;
            width: 200px;
        }

        .excel-search i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #858796;
        }

        .excel-filter-btn {
            background: #4e73df;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .excel-filter-btn:hover {
            background: #2e59d9;
        }

        .excel-filter-btn.active {
            background: #1cc88a;
        }

        .excel-table-wrapper {
            overflow-x: auto;
            max-height: 70vh;
            border: 1px solid #d1d3e2;
        }

        .excel-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 0.875rem;
            background: white;
        }

        .excel-table thead {
            background: #f8f9fc;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .excel-table th {
            border: 1px solid #d1d3e2;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #5a5c69;
            background: #f8f9fc;
            position: relative;
            cursor: pointer;
            user-select: none;
            white-space: nowrap;
        }

        .excel-table th:hover {
            background: #eaecf4;
        }

        .excel-table th.sortable::after {
            content: '↕';
            position: absolute;
            right: 8px;
            color: #858796;
            font-size: 0.8rem;
        }

        .excel-table th.sort-asc::after {
            content: '↑';
            color: #4e73df;
        }

        .excel-table th.sort-desc::after {
            content: '↓';
            color: #4e73df;
        }

        .excel-table td {
            border: 1px solid #d1d3e2;
            padding: 10px 8px;
            vertical-align: middle;
            background: white;
            transition: background-color 0.2s ease;
        }

        .excel-table tbody tr:hover {
            background: #f8f9fc;
        }

        .excel-table tbody tr:nth-child(even) {
            background: #fafbfc;
        }

        .excel-table tbody tr:nth-child(even):hover {
            background: #f1f3f6;
        }

        .excel-table .row-number {
            background: #f8f9fc;
            font-weight: 600;
            color: #858796;
            text-align: center;
            width: 50px;
            border-right: 2px solid #d1d3e2;
        }

        .status-cell {
            text-align: center;
            padding: 5px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 80px;
        }

        .status-selesai {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-proses {
            background: #cce7f0;
            color: #0c5460;
            border: 1px solid #b8daff;
        }

        .status-batal {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .excel-stats {
            background: #f8f9fc;
            border-top: 1px solid #d1d3e2;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
            color: #5a5c69;
        }

        .excel-stats-left {
            display: flex;
            gap: 20px;
        }

        .excel-stats-right {
            display: flex;
            gap: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-item i {
            font-size: 0.8rem;
        }

        .progress-mini {
            width: 100px;
            height: 6px;
            background: #e3e6f0;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-mini-bar {
            height: 100%;
            background: #1cc88a;
            transition: width 0.5s ease;
        }

        .excel-actions {
            display: flex;
            gap: 5px;
        }

        .excel-btn {
            background: #f8f9fc;
            border: 1px solid #d1d3e2;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .excel-btn:hover {
            background: #e3e6f0;
        }

        .excel-btn.active {
            background: #4e73df;
            color: white;
            border-color: #4e73df;
        }

        @media (max-width: 768px) {
            .excel-toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .excel-toolbar-left,
            .excel-toolbar-right {
                justify-content: center;
            }

            .excel-search input {
                width: 100%;
            }
        }
    </style>

</head>
<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="client.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">antosa</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="client.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>         
            </li>
          
            
            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Interface
            </div>
            
             <li class="nav-item active">
                <a class="nav-link" href="lihat_progress.php">
                    <i class="fas fa-fw fa-chart-line"></i>
                    <span>Lihat Progress</span></a>
            </li>

             <li class="nav-item">
                <a class="nav-link" href="file_management.php">
                    <i class="fas fa-fw fa-folder"></i>
                    <span>File Management</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

             <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>
            
            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <h1>Progress Proyek</h1>

                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="fas fa-table mr-2"></i>Progress Proyek - Data Sheet
                        </h1>
                        <div class="d-flex align-items-center">
                            <span class="badge badge-primary mr-2">
                                <i class="fas fa-database mr-1"></i><?php echo $total_tugas; ?> Records
                            </span>
                        </div>
                    </div>

                    <!-- Excel-like Table Container -->
                    <div class="excel-container">
                        <!-- Toolbar -->
                        <div class="excel-toolbar">
                            <div class="excel-toolbar-left">
                                <div class="excel-search">
                                    <i class="fas fa-search"></i>
                                    <input type="text" id="searchInput" placeholder="Cari tugas, deskripsi, atau tanggal..." onkeyup="searchTable()">
                                </div>
                                <div class="excel-actions">
                                    <button class="excel-filter-btn active" onclick="filterStatus('all')" id="filter-all">
                                        <i class="fas fa-list mr-1"></i>Semua
                                    </button>
                                    <button class="excel-filter-btn" onclick="filterStatus('selesai')" id="filter-selesai">
                                        <i class="fas fa-check mr-1"></i>Selesai
                                    </button>
                                    <button class="excel-filter-btn" onclick="filterStatus('proses')" id="filter-proses">
                                        <i class="fas fa-cog mr-1"></i>Proses
                                    </button>
                                    <button class="excel-filter-btn" onclick="filterStatus('batal')" id="filter-batal">
                                        <i class="fas fa-times mr-1"></i>Batal
                                    </button>
                                </div>
                            </div>
                            <div class="excel-toolbar-right">
                                <button class="excel-btn" onclick="exportData()">
                                    <i class="fas fa-download mr-1"></i>Export
                                </button>
                                <button class="excel-btn" onclick="refreshData()">
                                    <i class="fas fa-sync mr-1"></i>Refresh
                                </button>
                            </div>
                        </div>

                        <!-- Table -->
                        <div class="excel-table-wrapper">
                            <table class="excel-table" id="progressTable">
                                <thead>
                                    <tr>
                                        <th class="row-number">#</th>
                                        <th class="sortable" onclick="sortTable(1)">
                                            <i class="fas fa-tasks mr-2"></i>Nama Kegiatan
                                        </th>
                                        <th class="sortable" onclick="sortTable(2)">
                                            <i class="fas fa-align-left mr-2"></i>Deskripsi
                                        </th>
                                        <th class="sortable" onclick="sortTable(3)">
                                            <i class="fas fa-calendar mr-2"></i>Tanggal
                                        </th>
                                        <th class="sortable" onclick="sortTable(4)">
                                            <i class="fas fa-flag mr-2"></i>Status
                                        </th>
                                        <th>
                                            <i class="fas fa-chart-pie mr-2"></i>Progress
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <?php
                                    // Reset result pointer
                                    mysqli_data_seek($result, 0);
                                    $no = 1;
                                    while($row = mysqli_fetch_array($result)) {
                                        // Tentukan class status
                                        $status_class = '';
                                        switch($row['status']) {
                                            case 'pending':
                                                $status_class = 'status-proses';
                                                break;
                                            case 'proses':
                                                $status_class = 'status-proses';
                                                break;
                                            case 'verifikasi':
                                                $status_class = 'status-proses';
                                                break;
                                            case 'selesai':
                                                $status_class = 'status-selesai';
                                                break;
                                            case 'batal':
                                                $status_class = 'status-batal';
                                                break;
                                        }

                                        // Format tanggal
                                        $tanggal_formatted = date('d/m/Y', strtotime($row['tgl']));
                                        $tanggal_sort = date('Y-m-d', strtotime($row['tgl']));
                                    ?>
                                    <tr data-status="<?php echo $row['status']; ?>" data-date="<?php echo $tanggal_sort; ?>">
                                        <td class="row-number"><?php echo $no++; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($row['nama_kegiatan']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="text-muted"><?php echo htmlspecialchars($row['deskripsi']); ?></span>
                                        </td>
                                        <td data-sort="<?php echo $tanggal_sort; ?>">
                                            <i class="fas fa-calendar-day mr-2 text-muted"></i>
                                            <?php echo $tanggal_formatted; ?>
                                            <?php if($row['tgl_mulai']): ?>
                                                <br><small class="text-muted">Mulai: <?php echo date('d/m/Y', strtotime($row['tgl_mulai'])); ?></small>
                                            <?php endif; ?>
                                            <?php if($row['tgl_selesai']): ?>
                                                <br><small class="text-success">Selesai: <?php echo date('d/m/Y', strtotime($row['tgl_selesai'])); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td class="status-cell">
                                            <span class="status-badge <?php echo $status_class; ?>">
                                                <?php
                                                $status_display = '';
                                                switch($row['status']) {
                                                    case 'pending': $status_display = 'Pending'; break;
                                                    case 'proses': $status_display = 'Dalam Proses'; break;
                                                    case 'verifikasi': $status_display = 'Verifikasi'; break;
                                                    case 'selesai': $status_display = 'Selesai'; break;
                                                    case 'batal': $status_display = 'Batal'; break;
                                                    default: $status_display = ucfirst($row['status']);
                                                }
                                                echo $status_display;
                                                ?>
                                            </span>
                                            <?php if($row['verified_at']): ?>
                                                <br><small class="text-muted">Verified: <?php echo date('d/m/Y H:i', strtotime($row['verified_at'])); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress-mini mr-2">
                                                    <div class="progress-mini-bar" style="width: <?php echo $row['progress_percentage']; ?>%"></div>
                                                </div>
                                                <small class="text-muted"><?php echo $row['progress_percentage']; ?>%</small>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Stats Footer -->
                        <div class="excel-stats">
                            <div class="excel-stats-left">
                                <div class="stat-item">
                                    <i class="fas fa-list text-primary"></i>
                                    <span>Total: <strong id="totalRows"><?php echo $total_tugas; ?></strong></span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-check text-success"></i>
                                    <span>Selesai: <strong><?php echo $tugas_selesai; ?></strong></span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-cog text-info"></i>
                                    <span>Proses: <strong><?php echo $tugas_proses; ?></strong></span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-times text-danger"></i>
                                    <span>Batal: <strong><?php echo $tugas_batal; ?></strong></span>
                                </div>
                            </div>
                            <div class="excel-stats-right">
                                <div class="stat-item">
                                    <span>Progress Keseluruhan:</span>
                                    <div class="progress-mini ml-2">
                                        <div class="progress-mini-bar" style="width: <?php echo $progress_percentage; ?>%"></div>
                                    </div>
                                    <strong class="ml-2"><?php echo $progress_percentage; ?>%</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy;FOKUS UKK!!</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

<!-- Bootstrap core JavaScript -->
<script src="../tmp/vendor/jquery/jquery.min.js"></script>
<script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

<!-- Core plugin JavaScript -->
<script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

<!-- Custom scripts for all pages -->
<script src="../tmp/js/sb-admin-2.min.js"></script>

<!-- Custom JavaScript for Excel-like Table -->
<script>
let sortDirection = {};

// Search functionality
function searchTable() {
    const input = document.getElementById('searchInput');
    const filter = input.value.toLowerCase();
    const table = document.getElementById('progressTable');
    const tbody = table.getElementsByTagName('tbody')[0];
    const rows = tbody.getElementsByTagName('tr');
    let visibleCount = 0;

    for (let i = 0; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        let found = false;

        // Search in nama kegiatan, deskripsi, and tanggal
        for (let j = 1; j < cells.length - 1; j++) {
            if (cells[j].textContent.toLowerCase().indexOf(filter) > -1) {
                found = true;
                break;
            }
        }

        if (found) {
            rows[i].style.display = '';
            visibleCount++;
        } else {
            rows[i].style.display = 'none';
        }
    }

    updateRowCount(visibleCount);
}

// Filter by status
function filterStatus(status) {
    // Update active button
    document.querySelectorAll('.excel-filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById('filter-' + status).classList.add('active');

    const table = document.getElementById('progressTable');
    const tbody = table.getElementsByTagName('tbody')[0];
    const rows = tbody.getElementsByTagName('tr');
    let visibleCount = 0;

    for (let i = 0; i < rows.length; i++) {
        const rowStatus = rows[i].getAttribute('data-status');

        if (status === 'all' || rowStatus === status) {
            rows[i].style.display = '';
            visibleCount++;
        } else {
            rows[i].style.display = 'none';
        }
    }

    updateRowCount(visibleCount);
}

// Sort table
function sortTable(columnIndex) {
    const table = document.getElementById('progressTable');
    const tbody = table.getElementsByTagName('tbody')[0];
    const rows = Array.from(tbody.getElementsByTagName('tr'));
    const header = table.getElementsByTagName('th')[columnIndex];

    // Determine sort direction
    const currentDirection = sortDirection[columnIndex] || 'asc';
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    sortDirection[columnIndex] = newDirection;

    // Update header classes
    document.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    header.classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');

    // Sort rows
    rows.sort((a, b) => {
        let aValue, bValue;

        if (columnIndex === 3) { // Date column
            aValue = a.getAttribute('data-date');
            bValue = b.getAttribute('data-date');
        } else {
            aValue = a.getElementsByTagName('td')[columnIndex].textContent.trim();
            bValue = b.getElementsByTagName('td')[columnIndex].textContent.trim();
        }

        if (newDirection === 'asc') {
            return aValue.localeCompare(bValue);
        } else {
            return bValue.localeCompare(aValue);
        }
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));

    // Update row numbers
    updateRowNumbers();
}

// Update row numbers
function updateRowNumbers() {
    const tbody = document.getElementById('tableBody');
    const rows = tbody.getElementsByTagName('tr');
    let visibleIndex = 1;

    for (let i = 0; i < rows.length; i++) {
        if (rows[i].style.display !== 'none') {
            rows[i].getElementsByClassName('row-number')[0].textContent = visibleIndex++;
        }
    }
}

// Update row count
function updateRowCount(count) {
    document.getElementById('totalRows').textContent = count;
    updateRowNumbers();
}

// Export data (placeholder)
function exportData() {
    alert('Export functionality akan segera tersedia!');
}

// Refresh data
function refreshData() {
    location.reload();
}

// Initialize table
document.addEventListener('DOMContentLoaded', function() {
    // Add row hover effects
    const rows = document.querySelectorAll('#progressTable tbody tr');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fc';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // Animate progress bars
    const progressBars = document.querySelectorAll('.progress-mini-bar');
    progressBars.forEach((bar, index) => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, index * 50);
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+F for search
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            document.getElementById('searchInput').focus();
        }

        // Escape to clear search
        if (e.key === 'Escape') {
            document.getElementById('searchInput').value = '';
            searchTable();
        }
    });
});

// Add resize observer for responsive table
if (window.ResizeObserver) {
    const resizeObserver = new ResizeObserver(entries => {
        // Handle responsive behavior if needed
    });

    resizeObserver.observe(document.querySelector('.excel-table-wrapper'));
}
</script>

</body>
</html>
